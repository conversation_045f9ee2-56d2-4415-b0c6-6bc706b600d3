'use client'

import { useTranslations } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Gift, Trophy, Star, Zap, Crown, Award, Target, TrendingUp } from 'lucide-react'
import { useEffect, useState } from 'react'

interface LeagueConfig {
  id: string
  levels_per_league: number
  league_1_points: number
  league_2_points: number
  league_3_points: number
  league_4_points: number
  league_5_points: number
  league_1_discount: number
  league_2_discount: number
  league_3_discount: number
  league_4_discount: number
  league_5_discount: number
  global_multiplier: number
  multiplier_description: string
  is_active: boolean
}

export default function RegaliPage() {
  const t = useTranslations('account.gifts')
  const [leagueConfig, setLeagueConfig] = useState<LeagueConfig | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchLeagueConfig = async () => {
      try {
        const response = await fetch('/api/admin/gamification/league-config')
        if (response.ok) {
          const data = await response.json()
          setLeagueConfig(data)
        }
      } catch (error) {
        console.error('Error fetching league config:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchLeagueConfig()
  }, [])

  // Genera i dati delle leghe dinamicamente dalla configurazione
  const leagues = leagueConfig ? [
    { number: 1, name: 'Bronze', levels: '1-10', discount: leagueConfig.league_1_discount, color: 'bg-amber-600' },
    { number: 2, name: 'Silver', levels: '11-20', discount: leagueConfig.league_2_discount, color: 'bg-gray-400' },
    { number: 3, name: 'Gold', levels: '21-30', discount: leagueConfig.league_3_discount, color: 'bg-yellow-500' },
    { number: 4, name: 'Platinum', levels: '31-40', discount: leagueConfig.league_4_discount, color: 'bg-blue-500' },
    { number: 5, name: 'Diamond', levels: '41+', discount: leagueConfig.league_5_discount, color: 'bg-purple-600' }
  ] : [
    // Fallback con valori predefiniti se non si riesce a caricare la configurazione
    { number: 1, name: 'Bronze', levels: '1-10', discount: 0, color: 'bg-amber-600' },
    { number: 2, name: 'Silver', levels: '11-20', discount: 5, color: 'bg-gray-400' },
    { number: 3, name: 'Gold', levels: '21-30', discount: 10, color: 'bg-yellow-500' },
    { number: 4, name: 'Platinum', levels: '31-40', discount: 15, color: 'bg-blue-500' },
    { number: 5, name: 'Diamond', levels: '41+', discount: 20, color: 'bg-purple-600' }
  ]

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full mb-6">
              <Gift className="h-10 w-10 text-purple-600" />
            </div>
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-96 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full mb-6">
            <Gift className="h-10 w-10 text-purple-600" />
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold mb-4">
            {t('pageTitle')}
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            {t('pageDescription')}
          </p>
        </div>

        {/* Come Funziona */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-6 w-6 text-primary" />
              {t('howItWorks.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">{t('howItWorks.step1.title')}</h3>
                <p className="text-sm text-muted-foreground">{t('howItWorks.step1.description')}</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Trophy className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">{t('howItWorks.step2.title')}</h3>
                <p className="text-sm text-muted-foreground">{t('howItWorks.step2.description')}</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Gift className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">{t('howItWorks.step3.title')}</h3>
                <p className="text-sm text-muted-foreground">{t('howItWorks.step3.description')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sistema Leghe */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-6 w-6 text-primary" />
              {t('leagueSystem.title')}
            </CardTitle>
            <p className="text-muted-foreground">{t('leagueSystem.description')}</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {leagues.map((league) => (
                <div key={league.number} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className={`w-4 h-4 rounded-full ${league.color}`}></div>
                    <div>
                      <h4 className="font-semibold">{t('leagueSystem.league')} {league.number}: {league.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {t('leagueSystem.levels')}: {league.levels}
                      </p>
                    </div>
                  </div>
                  <Badge variant="secondary">
                    {league.discount}% {t('leagueSystem.discount')}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Tipi di Regali */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-6 w-6 text-yellow-500" />
                {t('giftTypes.levelGifts.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                {t('giftTypes.levelGifts.description')}
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{t('giftTypes.levelGifts.frequency')}</span>
                  <Badge variant="outline">{t('giftTypes.levelGifts.frequencyValue')}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>{t('giftTypes.levelGifts.availability')}</span>
                  <Badge variant="outline">{t('giftTypes.levelGifts.availabilityValue')}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-6 w-6 text-purple-500" />
                {t('giftTypes.leagueGifts.title')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                {t('giftTypes.leagueGifts.description')}
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>{t('giftTypes.leagueGifts.frequency')}</span>
                  <Badge variant="outline">{t('giftTypes.leagueGifts.frequencyValue')}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>{t('giftTypes.leagueGifts.priority')}</span>
                  <Badge variant="outline">{t('giftTypes.leagueGifts.priorityValue')}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Regole Importanti */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-6 w-6 text-orange-500" />
              {t('importantRules.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm">{t('importantRules.rule1')}</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm">{t('importantRules.rule2')}</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm">{t('importantRules.rule3')}</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm">{t('importantRules.rule4')}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Come Riscattare */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-6 w-6 text-green-500" />
              {t('howToRedeem.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-muted-foreground">{t('howToRedeem.description')}</p>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-semibold mb-2">{t('howToRedeem.step1.title')}</h4>
                  <p className="text-sm text-muted-foreground">{t('howToRedeem.step1.description')}</p>
                </div>
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-semibold mb-2">{t('howToRedeem.step2.title')}</h4>
                  <p className="text-sm text-muted-foreground">{t('howToRedeem.step2.description')}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
